*.tar
*.tar.gz
*.zip
venv*/
envs/
slurm_logs/

sync1.sh
data_preprocess_pj1
data-preparation1
__pycache__
*.log
*.pyc
.vscode
debug/
*.ipynb
.idea
*.egg-info/

# vscode history
.history

.DS_Store
.env

bad_words/
bak/

app/tests/*
temp/
tmp/
tmp
.vscode
.vscode/
ocr_demo
.coveragerc
/app/common/__init__.py
source.dev.env

tmp

projects/web/node_modules
projects/web/dist

projects/web_demo/web_demo/static/
cli_debug/
debug_utils/

# sphinx docs
_build/

# monkeyocr
output/
build/
model_weight